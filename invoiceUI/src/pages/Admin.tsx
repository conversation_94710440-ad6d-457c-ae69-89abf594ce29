import { useState, useEffect, useCallback } from "react"
import {
  Bell,
  Search,
  User,
  Settings,
  Plus,
  Trash2,
  X,
  Mail,
  Building,
  Shield,
  Users,
  Edit,
  Check,
  Database,
  FileText,
  RotateCcw,
  Activity,
} from "lucide-react"
import { OutlookLogo, <PERSON><PERSON><PERSON>s<PERSON>ogo } from "@/components/ui/logos"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { <PERSON> } from "react-router-dom"
import { getOutlookAuthUrl, getOutlookConnections, connectQuickBooks, getQuickBooksConnections, type OutlookConnection, type QuickBooksConnectionData } from "@/utils/api"
import { QuickBooksTestForm } from "@/components/QuickBooksTestForm"

// Mock data for users
const mockUsers = [
  {
    id: "1",
    name: "Sarah Johnson",
    email: "<EMAIL>",
    role: "Admin",
    status: "Active",
    lastLogin: "2025-01-16 09:30 AM",
    createdAt: "2024-12-01",
    department: "Finance",
    phone: "+****************",
  },
  {
    id: "2",
    name: "Mike Chen",
    email: "<EMAIL>",
    role: "User",
    status: "Active",
    lastLogin: "2025-01-15 02:15 PM",
    createdAt: "2024-12-15",
    department: "Operations",
    phone: "+****************",
  },
  {
    id: "3",
    name: "Emily Davis",
    email: "<EMAIL>",
    role: "User",
    status: "Inactive",
    lastLogin: "2025-01-10 11:45 AM",
    createdAt: "2025-01-05",
    department: "Marketing",
    phone: "+****************",
  },
]

// Mock mailbox connections - removed, now using real API data

// QuickBooks integration state interface
interface QuickBooksIntegrationState {
  connected: boolean
  companyName: string
  realmId: string
  accessToken: string
  refreshToken: string
  lastSync: string
  status: string
  connectedAt: string
  environment: string
}

// Default QuickBooks integration state (disconnected)
const defaultQuickBooksIntegration: QuickBooksIntegrationState = {
  connected: false,
  companyName: "",
  realmId: "",
  accessToken: "",
  refreshToken: "",
  lastSync: "",
  status: "Not Connected",
  connectedAt: "",
  environment: "",
}

// Mock system settings
const mockSystemSettings = {
  dataRetention: {
    invoiceData: 365, // days
    auditLogs: 90, // days
    userActivity: 30, // days
  },
  auditLogging: true,
  emailNotifications: true,
  autoProcessing: true,
  backupFrequency: "daily",
  maintenanceMode: false,
}

export default function Admin() {
  const [users, setUsers] = useState(mockUsers)
  const [mailboxes, setMailboxes] = useState<OutlookConnection[]>([])
  const [qbIntegration, setQbIntegration] = useState(defaultQuickBooksIntegration)
  const [systemSettings, setSystemSettings] = useState(mockSystemSettings)

  // Loading and error states for mailboxes
  const [isLoadingMailboxes, setIsLoadingMailboxes] = useState(false)
  const [mailboxError, setMailboxError] = useState("")

  // Dialog states
  const [showAddUser, setShowAddUser] = useState(false)
  const [showEditUser, setShowEditUser] = useState(false)
  const [showOutlookConnect, setShowOutlookConnect] = useState(false)
  const [showQBConnect, setShowQBConnect] = useState(false)

  // Form states
  const [newUser, setNewUser] = useState({
    name: "",
    email: "",
    role: "User",
    department: "",
    phone: "",
  })
  const [editingUser, setEditingUser] = useState<any>(null)
  const [qbConnectionData, setQbConnectionData] = useState<QuickBooksConnectionData>({
    realm_id: "",
    client_id: "",
    client_secret: "",
    access_token: "",
    refresh_token: "",
    token_expires_at: "",
    environment: "sandbox",
  })

  // User management handlers
  const handleAddUser = () => {
    const user = {
      id: Date.now().toString(),
      ...newUser,
      status: "Active",
      lastLogin: "Never",
      createdAt: new Date().toISOString().split("T")[0],
    }
    setUsers([...users, user])
    setNewUser({ name: "", email: "", role: "User", department: "", phone: "" })
    setShowAddUser(false)
  }

  const handleEditUser = (user: any) => {
    setEditingUser(user)
    setShowEditUser(true)
  }

  const handleUpdateUser = () => {
    setUsers(users.map(user => user.id === editingUser.id ? editingUser : user))
    setShowEditUser(false)
    setEditingUser(null)
  }

  const handleDeleteUser = (userId: string) => {
    setUsers(users.filter((user) => user.id !== userId))
  }

  // Fetch mailbox connections from API
  const fetchMailboxConnections = useCallback(async () => {
    setIsLoadingMailboxes(true)
    setMailboxError("")

    try {
      const response = await getOutlookConnections()
      if (response.success) {
        setMailboxes(response.connections)
      } else {
        throw new Error(response.message || "Failed to fetch connections")
      }
    } catch (error) {
      console.error("Error fetching mailbox connections:", error)
      setMailboxError(error instanceof Error ? error.message : "Failed to load mailbox connections")
    } finally {
      setIsLoadingMailboxes(false)
    }
  }, [])

  // Fetch QuickBooks connection status
  const fetchQuickBooksConnection = useCallback(async () => {
    try {
      const response = await getQuickBooksConnections()
      if (response.success && response.connections.length > 0) {
        // Get the first active connection
        const activeConnection = response.connections.find(conn => conn.is_active) || response.connections[0]

        setQbIntegration((prev: QuickBooksIntegrationState) => ({
          ...prev,
          connected: true,
          status: "Active",
          realmId: activeConnection.realm_id,
          environment: activeConnection.environment,
          companyName: activeConnection.company_info?.name || "Connected Company",
          lastSync: activeConnection.last_tested || "Never",
        }))
      }
    } catch (error) {
      console.error("Error fetching QuickBooks connection:", error)
      // Don't show error to user for initial fetch, just keep default state
    }
  }, [])

  // Auto-refresh mailbox connections every 30 seconds
  useEffect(() => {
    fetchMailboxConnections()
    fetchQuickBooksConnection()

    const interval = setInterval(fetchMailboxConnections, 30000) // 30 seconds

    return () => clearInterval(interval)
  }, [fetchMailboxConnections, fetchQuickBooksConnection])

  // Mailbox handlers
  const [isConnecting, setIsConnecting] = useState(false)
  const [connectionError, setConnectionError] = useState("")

  const handleOutlookConnect = async () => {
    setIsConnecting(true)
    setConnectionError("")

    try {
      // Call the Outlook authorization API
      const authResponse = await getOutlookAuthUrl(true)

      if (authResponse.success && authResponse.auth_url) {
        // Open the authorization URL in a new window/tab
        window.open(authResponse.auth_url, '_blank', 'width=600,height=700,scrollbars=yes,resizable=yes')

        // For now, we'll simulate the connection process
        // In a real implementation, you'd wait for the OAuth callback
        // and then create the mailbox entry after successful authorization

        // Show a message to the user about the OAuth process
        alert("Please complete the authorization in the opened window. Once authorized, your Outlook account will be connected.")

        // Close the dialog for now
        setShowOutlookConnect(false)

        // Refresh connections after a short delay to allow for OAuth completion
        setTimeout(() => {
          fetchMailboxConnections()
        }, 5000)

        // TODO: Implement proper OAuth callback handling
        // This would typically involve:
        // 1. Listening for the OAuth callback
        // 2. Exchanging the authorization code for tokens
        // 3. Creating the mailbox entry in the backend
        // 4. Updating the UI with the new mailbox

      } else {
        throw new Error(authResponse.message || "Failed to get authorization URL")
      }
    } catch (error) {
      console.error("Outlook connection error:", error)
      setConnectionError(error instanceof Error ? error.message : "Failed to connect to Outlook")
    } finally {
      setIsConnecting(false)
    }
  }

  // Function to handle successful OAuth callback (for future implementation)
  // const handleOutlookConnected = (mailboxData: any) => {
  //   const newMailbox = {
  //     id: Date.now().toString(),
  //     provider: "Outlook",
  //     email: mailboxData.email || "<EMAIL>",
  //     status: "Connected",
  //     lastSync: new Date().toLocaleString(),
  //     syncInterval: "15 minutes",
  //     messagesProcessed: 0,
  //     connectedAt: new Date().toISOString().split("T")[0],
  //   }
  //   setMailboxes([...mailboxes, newMailbox])
  // }

  const handleDisconnectMailbox = async (mailboxId: string) => {
    // TODO: Implement API call to disconnect mailbox
    // For now, just remove from local state and refresh
    setMailboxes(mailboxes.filter(mb => mb.connection_id !== mailboxId))

    // Refresh connections to get updated state from server
    setTimeout(() => {
      fetchMailboxConnections()
    }, 1000)
  }

  // Mailbox configuration handler
  const [showMailboxConfig, setShowMailboxConfig] = useState(false)
  const [configuringMailbox, setConfiguringMailbox] = useState<any>(null)

  const handleMailboxConfigure = (mailbox: any) => {
    setConfiguringMailbox(mailbox)
    setShowMailboxConfig(true)
  }

  // QuickBooks handlers
  const [isConnectingQB, setIsConnectingQB] = useState(false)
  const [qbConnectionError, setQbConnectionError] = useState("")

  // Add event listener to catch unexpected navigation during QB connection
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (showQBConnect && isConnectingQB) {
        e.preventDefault()
        return 'Are you sure you want to leave? Your QuickBooks connection is in progress.'
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [showQBConnect, isConnectingQB])

  const handleQBConnect = async (e?: React.MouseEvent) => {
    console.log('handleQBConnect called')

    // Prevent any default behavior that might cause redirects
    if (e) {
      e.preventDefault()
      e.stopPropagation()
    }

    setIsConnectingQB(true)
    setQbConnectionError("")

    try {
      // Validate required fields
      if (!qbConnectionData.realm_id || !qbConnectionData.client_id || !qbConnectionData.client_secret ||
          !qbConnectionData.access_token || !qbConnectionData.refresh_token) {
        throw new Error("Please fill in all required fields")
      }

      // Call the QuickBooks connection API
      const response = await connectQuickBooks(qbConnectionData)

      if (response.success) {
        // Update the integration state with successful connection
        setQbIntegration((prev: QuickBooksIntegrationState) => ({
          ...prev,
          connected: response.connection_status.is_connected,
          status: response.connection_status.token_status === "valid" ? "Active" : "Error",
          connectedAt: new Date().toISOString().split("T")[0],
          realmId: response.connection_status.realm_id,
          environment: response.connection_status.environment,
          companyName: response.connection_status.company_name || "Connected Company",
          lastSync: response.connection_status.last_tested || "Never",
          accessToken: qbConnectionData.access_token.substring(0, 20) + "...", // Show partial token
          refreshToken: qbConnectionData.refresh_token.substring(0, 20) + "...", // Show partial token
        }))
        setShowQBConnect(false)

        // Reset form data
        setQbConnectionData({
          realm_id: "",
          client_id: "",
          client_secret: "",
          access_token: "",
          refresh_token: "",
          token_expires_at: "",
          environment: "sandbox",
        })
      } else {
        throw new Error(response.message || "Failed to connect QuickBooks")
      }
    } catch (error) {
      console.error("Error connecting QuickBooks:", error)
      setQbConnectionError(error instanceof Error ? error.message : "Failed to connect QuickBooks")
    } finally {
      setIsConnectingQB(false)
    }
  }

  const handleQBDisconnect = () => {
    console.log('Disconnecting QuickBooks...')

    // Reset the integration state to default values
    setQbIntegration({
      connected: false,
      status: "Not Connected",
      companyName: "",
      lastSync: "",
      connectedAt: "",
      realmId: "",
      environment: "",
      accessToken: "",
      refreshToken: "",
    })

    // Also clear any form data
    setQbConnectionData({
      realm_id: "",
      client_id: "",
      client_secret: "",
      access_token: "",
      refresh_token: "",
      token_expires_at: "",
      environment: "sandbox",
    })

    // Clear any errors
    setQbConnectionError("")

    console.log('QuickBooks disconnected successfully')
  }

  // System settings handlers
  const handleSystemSettingChange = (key: string, value: any) => {
    setSystemSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleDataRetentionChange = (key: string, value: number) => {
    setSystemSettings(prev => ({
      ...prev,
      dataRetention: {
        ...prev.dataRetention,
        [key]: value
      }
    }))
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-8">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-orange-400 to-pink-400 rounded-lg flex items-center justify-center">
                <div className="w-4 h-4 bg-white rounded-sm"></div>
              </div>
            </div>
            <div className="flex items-center space-x-6">
              <Link to="/dashboard">
                <Button variant="ghost" className="text-gray-600 hover:text-gray-900 font-medium px-4 py-2 h-auto">
                  Dashboard
                </Button>
              </Link>
              <Link to="/invoices">
                <Button variant="ghost" className="text-gray-600 hover:text-gray-900 font-medium px-4 py-2 h-auto">
                  Invoices
                </Button>
              </Link>
              <div className="relative">
                <Button variant="ghost" className="text-orange-500 font-medium px-4 py-2 h-auto">
                  Admin Settings
                </Button>
                <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-orange-500 translate-y-5"></div>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon">
              <Search className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon">
              <Bell className="h-5 w-5" />
            </Button>
            <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
              <User className="h-4 w-4 text-white" />
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-1 flex items-center">
            <Settings className="h-6 w-6 mr-3" />
            Admin Settings
          </h1>
          <p className="text-gray-600">Manage users, integrations, and system settings</p>
        </div>

        {/* Tabbed Interface */}
        <Tabs defaultValue="users" className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-8">
            <TabsTrigger value="users" className="flex items-center space-x-2">
              <Users className="h-4 w-4" />
              <span>User Settings</span>
            </TabsTrigger>
            <TabsTrigger value="mailbox" className="flex items-center space-x-2">
              <Mail className="h-4 w-4" />
              <span>Mailbox Connections</span>
            </TabsTrigger>
            <TabsTrigger value="accounting" className="flex items-center space-x-2">
              <Building className="h-4 w-4" />
              <span>Accounting Integrations</span>
            </TabsTrigger>
            <TabsTrigger value="system" className="flex items-center space-x-2">
              <Database className="h-4 w-4" />
              <span>System Settings</span>
            </TabsTrigger>
          </TabsList>

          {/* User Settings Tab */}
          <TabsContent value="users" className="space-y-6">
            <Card className="bg-white shadow-sm">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
                      <Users className="h-5 w-5 mr-2" />
                      User Management
                    </CardTitle>
                    <p className="text-sm text-gray-600 mt-1">Manage user accounts and permissions</p>
                  </div>
                  <Dialog open={showAddUser} onOpenChange={setShowAddUser}>
                    <DialogTrigger asChild>
                      <Button className="bg-orange-500 hover:bg-orange-600 text-white" size="sm">
                        <Plus className="h-4 w-4 mr-2" />
                        Add User
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md">
                      <DialogHeader>
                        <DialogTitle>Add New User</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="name">Full Name</Label>
                          <Input
                            id="name"
                            value={newUser.name}
                            onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                            placeholder="Enter full name"
                          />
                        </div>
                        <div>
                          <Label htmlFor="email">Email Address</Label>
                          <Input
                            id="email"
                            type="email"
                            value={newUser.email}
                            onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                            placeholder="Enter email address"
                          />
                        </div>
                        <div>
                          <Label htmlFor="department">Department</Label>
                          <Input
                            id="department"
                            value={newUser.department}
                            onChange={(e) => setNewUser({ ...newUser, department: e.target.value })}
                            placeholder="Enter department"
                          />
                        </div>
                        <div>
                          <Label htmlFor="phone">Phone Number</Label>
                          <Input
                            id="phone"
                            value={newUser.phone}
                            onChange={(e) => setNewUser({ ...newUser, phone: e.target.value })}
                            placeholder="Enter phone number"
                          />
                        </div>
                        <div>
                          <Label htmlFor="role">Role</Label>
                          <Select
                            value={newUser.role}
                            onValueChange={(value) => setNewUser({ ...newUser, role: value })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="User">User</SelectItem>
                              <SelectItem value="Admin">Admin</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="flex justify-end space-x-2 pt-4">
                          <Button variant="outline" onClick={() => setShowAddUser(false)}>
                            Cancel
                          </Button>
                          <Button onClick={handleAddUser} className="bg-orange-500 hover:bg-orange-600">
                            Add User
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>

                  {/* Edit User Dialog */}
                  <Dialog open={showEditUser} onOpenChange={setShowEditUser}>
                    <DialogContent className="max-w-md">
                      <DialogHeader>
                        <DialogTitle>Edit User</DialogTitle>
                      </DialogHeader>
                      {editingUser && (
                        <div className="space-y-4">
                          <div>
                            <Label htmlFor="editName">Full Name</Label>
                            <Input
                              id="editName"
                              value={editingUser.name}
                              onChange={(e) => setEditingUser({ ...editingUser, name: e.target.value })}
                              placeholder="Enter full name"
                            />
                          </div>
                          <div>
                            <Label htmlFor="editEmail">Email Address</Label>
                            <Input
                              id="editEmail"
                              type="email"
                              value={editingUser.email}
                              onChange={(e) => setEditingUser({ ...editingUser, email: e.target.value })}
                              placeholder="Enter email address"
                            />
                          </div>
                          <div>
                            <Label htmlFor="editDepartment">Department</Label>
                            <Input
                              id="editDepartment"
                              value={editingUser.department}
                              onChange={(e) => setEditingUser({ ...editingUser, department: e.target.value })}
                              placeholder="Enter department"
                            />
                          </div>
                          <div>
                            <Label htmlFor="editPhone">Phone Number</Label>
                            <Input
                              id="editPhone"
                              value={editingUser.phone}
                              onChange={(e) => setEditingUser({ ...editingUser, phone: e.target.value })}
                              placeholder="Enter phone number"
                            />
                          </div>
                          <div>
                            <Label htmlFor="editRole">Role</Label>
                            <Select
                              value={editingUser.role}
                              onValueChange={(value) => setEditingUser({ ...editingUser, role: value })}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="User">User</SelectItem>
                                <SelectItem value="Admin">Admin</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="flex justify-end space-x-2 pt-4">
                            <Button variant="outline" onClick={() => setShowEditUser(false)}>
                              Cancel
                            </Button>
                            <Button onClick={handleUpdateUser} className="bg-orange-500 hover:bg-orange-600">
                              Update User
                            </Button>
                          </div>
                        </div>
                      )}
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">User</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Email</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Department</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Role</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Status</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Last Login</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {users.map((user) => (
                        <tr key={user.id} className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="py-4 px-4">
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                                <User className="h-4 w-4 text-orange-600" />
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">{user.name}</div>
                                <div className="text-sm text-gray-500">{user.phone}</div>
                              </div>
                            </div>
                          </td>
                          <td className="py-4 px-4">
                            <span className="text-sm text-gray-700">{user.email}</span>
                          </td>
                          <td className="py-4 px-4">
                            <span className="text-sm text-gray-700">{user.department}</span>
                          </td>
                          <td className="py-4 px-4">
                            <Badge
                              variant="secondary"
                              className={`text-xs ${
                                user.role === "Admin" ? "bg-purple-100 text-purple-800" : "bg-blue-100 text-blue-800"
                              }`}
                            >
                              {user.role === "Admin" && <Shield className="h-3 w-3 mr-1" />}
                              {user.role}
                            </Badge>
                          </td>
                          <td className="py-4 px-4">
                            <Badge
                              variant="secondary"
                              className={`text-xs ${
                                user.status === "Active" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                              }`}
                            >
                              {user.status}
                            </Badge>
                          </td>
                          <td className="py-4 px-4">
                            <span className="text-sm text-gray-700">{user.lastLogin}</span>
                          </td>
                          <td className="py-4 px-4">
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-8 w-8 p-0 text-blue-600 hover:bg-blue-50 bg-transparent"
                                onClick={() => handleEditUser(user)}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-8 w-8 p-0 text-red-600 hover:bg-red-50 bg-transparent"
                                onClick={() => handleDeleteUser(user.id)}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Mailbox Connections Tab */}
          <TabsContent value="mailbox" className="space-y-6">
            <Card className="bg-white shadow-sm">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
                      <Mail className="h-5 w-5 mr-2" />
                      Email Mailbox Connections
                    </CardTitle>
                    <p className="text-sm text-gray-600 mt-1">Connect and manage email accounts for invoice processing</p>
                  </div>
                  <Dialog open={showOutlookConnect} onOpenChange={setShowOutlookConnect}>
                    <DialogTrigger asChild>
                      <Button className="bg-blue-600 hover:bg-blue-700 text-white" size="sm">
                        <OutlookLogo width={16} height={16} className="mr-2" />
                        Connect Outlook
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md">
                      <DialogHeader>
                        <DialogTitle className="flex items-center space-x-2">
                          <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center border border-gray-200">
                            <OutlookLogo width={20} height={20} />
                          </div>
                          <span>Connect Outlook Account</span>
                        </DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div className="text-center py-6">
                          <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4 border border-gray-200">
                            <OutlookLogo width={36} height={36} />
                          </div>
                          <h3 className="text-lg font-medium text-gray-900 mb-2">Connect to Microsoft Outlook</h3>
                          <p className="text-sm text-gray-600 mb-6">
                            We'll securely connect to your Outlook account to monitor for incoming invoices.
                          </p>
                          <div className="space-y-3">
                            <div className="flex items-center text-sm text-gray-600">
                              <Check className="h-4 w-4 text-green-500 mr-2" />
                              Read emails from specified folders
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                              <Check className="h-4 w-4 text-green-500 mr-2" />
                              Extract invoice attachments
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                              <Check className="h-4 w-4 text-green-500 mr-2" />
                              Secure OAuth 2.0 authentication
                            </div>
                          </div>
                        </div>

                        {/* Error Message */}
                        {connectionError && (
                          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                            <p className="text-sm text-red-600">{connectionError}</p>
                          </div>
                        )}

                        <div className="flex justify-end space-x-2 pt-4">
                          <Button
                            variant="outline"
                            onClick={() => {
                              setShowOutlookConnect(false)
                              setConnectionError("")
                            }}
                            disabled={isConnecting}
                          >
                            Cancel
                          </Button>
                          <Button
                            onClick={handleOutlookConnect}
                            className="bg-blue-600 hover:bg-blue-700"
                            disabled={isConnecting}
                          >
                            {isConnecting ? (
                              <>
                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                                Connecting...
                              </>
                            ) : (
                              <>
                                <OutlookLogo width={16} height={16} className="mr-2" />
                                Connect Account
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>

                  {/* Mailbox Configuration Dialog */}
                  <Dialog open={showMailboxConfig} onOpenChange={setShowMailboxConfig}>
                    <DialogContent className="max-w-lg">
                      <DialogHeader>
                        <DialogTitle className="flex items-center space-x-2">
                          <Settings className="h-5 w-5 text-blue-600" />
                          <span>Configure Mailbox Settings</span>
                        </DialogTitle>
                      </DialogHeader>
                      {configuringMailbox && (
                        <div className="space-y-6">
                          <div className="flex items-center space-x-4 p-4 bg-blue-50 rounded-lg">
                            <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                              <Mail className="h-6 w-6 text-white" />
                            </div>
                            <div>
                              <h3 className="font-medium text-gray-900">{configuringMailbox.email}</h3>
                              <p className="text-sm text-gray-600">{configuringMailbox.provider} Account</p>
                            </div>
                          </div>

                          <div className="space-y-4">
                            <div>
                              <Label htmlFor="syncInterval">Sync Interval</Label>
                              <Select defaultValue={configuringMailbox.syncInterval}>
                                <SelectTrigger className="mt-1">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="5 minutes">Every 5 minutes</SelectItem>
                                  <SelectItem value="15 minutes">Every 15 minutes</SelectItem>
                                  <SelectItem value="30 minutes">Every 30 minutes</SelectItem>
                                  <SelectItem value="1 hour">Every hour</SelectItem>
                                  <SelectItem value="4 hours">Every 4 hours</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div>
                              <Label htmlFor="folderPath">Monitor Folder</Label>
                              <Input
                                id="folderPath"
                                defaultValue="Inbox/Invoices"
                                placeholder="Enter folder path"
                                className="mt-1"
                              />
                              <p className="text-xs text-gray-500 mt-1">Specify which folder to monitor for invoices</p>
                            </div>

                            <div>
                              <Label htmlFor="fileTypes">Allowed File Types</Label>
                              <Input
                                id="fileTypes"
                                defaultValue="PDF, PNG, JPG, JPEG"
                                placeholder="Enter file types"
                                className="mt-1"
                              />
                              <p className="text-xs text-gray-500 mt-1">Comma-separated list of allowed attachment types</p>
                            </div>


                          </div>

                          <div className="flex justify-end space-x-2 pt-4">
                            <Button variant="outline" onClick={() => setShowMailboxConfig(false)}>
                              Cancel
                            </Button>
                            <Button className="bg-blue-600 hover:bg-blue-700" onClick={() => setShowMailboxConfig(false)}>
                              Save Configuration
                            </Button>
                          </div>
                        </div>
                      )}
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                {/* Error Message */}
                {mailboxError && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                    <div className="flex items-center">
                      <X className="h-5 w-5 text-red-500 mr-2" />
                      <p className="text-sm text-red-600">{mailboxError}</p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={fetchMailboxConnections}
                        className="ml-auto text-red-600 hover:text-red-700"
                      >
                        Retry
                      </Button>
                    </div>
                  </div>
                )}

                <div className="space-y-4">
                  {mailboxes.map((mailbox) => (
                    <div
                      key={mailbox.connection_id}
                      className="bg-white border border-gray-200 rounded-lg p-4 hover:border-gray-300 hover:shadow-sm transition-all duration-200"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-9 h-9 bg-white rounded-lg flex items-center justify-center border border-gray-200">
                            <OutlookLogo width={20} height={20} />
                          </div>

                          <div>
                            <h3 className="text-sm font-medium text-gray-900">{mailbox.user_email}</h3>
                            <div className="flex items-center space-x-2 text-xs text-gray-500 mt-0.5">
                              <span>Outlook</span>
                              <span>•</span>
                              <span>Connected {new Date(mailbox.created_at).toLocaleDateString()}</span>
                            </div>
                            <div className="mt-1">
                              <Badge
                                variant="secondary"
                                className={`text-xs ${
                                  mailbox.status === "Connected"
                                    ? "bg-green-100 text-green-800"
                                    : mailbox.status === "Syncing"
                                    ? "bg-blue-100 text-blue-800"
                                    : mailbox.status === "Error"
                                    ? "bg-red-100 text-red-800"
                                    : "bg-red-100 text-red-800"
                                }`}
                              >
                                {mailbox.status}
                              </Badge>
                            </div>
                          </div>
                        </div>

                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleMailboxConfigure(mailbox)}
                            className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 hover:border-gray-400 transition-colors duration-150"
                          >
                            <Settings className="w-3.5 h-3.5 mr-1" />
                            Configure
                          </button>
                          <button
                            onClick={() => handleDisconnectMailbox(mailbox.connection_id)}
                            className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-red-700 bg-white border border-gray-300 rounded hover:bg-red-50 hover:border-red-400 transition-colors duration-150"
                          >
                            <X className="w-3.5 h-3.5 mr-1" />
                            Disconnect
                          </button>
                        </div>
                      </div>

                      {/* Statistics */}
                      <div className="mt-4 pt-4 border-t border-gray-100">
                        <div className="grid grid-cols-3 gap-4 text-center">
                          <div>
                            <div className="text-base font-semibold text-gray-900">{mailbox.email_count.toLocaleString()}</div>
                            <div className="text-xs text-gray-500 mt-0.5">Emails</div>
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-700">
                              {mailbox.last_sync_at
                                ? new Date(mailbox.last_sync_at).toLocaleString()
                                : "Never"
                              }
                            </div>
                            <div className="text-xs text-gray-500 mt-0.5">Last Sync</div>
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-700">
                              {mailbox.sync_interval_seconds < 60
                                ? `${mailbox.sync_interval_seconds} seconds`
                                : `${Math.round(mailbox.sync_interval_seconds / 60)} minutes`
                              }
                            </div>
                            <div className="text-xs text-gray-500 mt-0.5">Sync Interval</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}

                  {mailboxes.length === 0 && !isLoadingMailboxes && !mailboxError && (
                    <div className="text-center py-12">
                      <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No mailboxes connected</h3>
                      <p className="text-gray-600 mb-4">Connect your first email account to start processing invoices automatically.</p>
                      <Button onClick={() => setShowOutlookConnect(true)} className="bg-blue-600 hover:bg-blue-700">
                        <Plus className="h-4 w-4 mr-2" />
                        Connect Outlook
                      </Button>
                    </div>
                  )}

                  {isLoadingMailboxes && mailboxes.length === 0 && (
                    <div className="text-center py-12">
                      <div className="w-8 h-8 border-4 border-gray-300 border-t-blue-600 rounded-full animate-spin mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Loading connections...</h3>
                      <p className="text-gray-600">Fetching your mailbox connections</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Accounting Integrations Tab */}
          <TabsContent value="accounting" className="space-y-6">
            <Card className="bg-white shadow-sm">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
                      <Building className="h-5 w-5 mr-2" />
                      Accounting System Integrations
                    </CardTitle>
                    <p className="text-sm text-gray-600 mt-1">Connect to your accounting software for seamless data sync</p>
                  </div>

                  {/* Debug Test Component */}
                  <div className="mb-4">
                    <QuickBooksTestForm />
                  </div>
                  {!qbIntegration.connected && (
                    <Dialog open={showQBConnect} onOpenChange={(open) => {
                      console.log('Dialog onOpenChange called with:', open)
                      // Only allow closing if not currently connecting
                      if (!isConnectingQB) {
                        setShowQBConnect(open)
                        if (!open) {
                          setQbConnectionError("")
                        }
                      }
                    }}>
                      <DialogTrigger asChild>
                        <Button className="bg-green-600 hover:bg-green-700 text-white" size="sm">
                          <QuickBooksLogo width={16} height={16} className="mr-2" />
                          Connect QuickBooks
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-lg">
                        <DialogHeader>
                          <DialogTitle className="flex items-center space-x-2">
                            <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center border border-gray-200">
                              <QuickBooksLogo width={20} height={20} />
                            </div>
                            <span>Connect QuickBooks Online</span>
                          </DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="text-center py-4">
                            <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4 border border-gray-200">
                              <QuickBooksLogo width={36} height={36} />
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">QuickBooks Online Integration</h3>
                            <p className="text-sm text-gray-600 mb-4">
                              Connect your QuickBooks Online account to automatically sync invoice data.
                            </p>
                          </div>

                          <form onSubmit={(e) => {
                            console.log('Form submit event triggered')
                            e.preventDefault()
                            e.stopPropagation()
                            return false
                          }} className="space-y-4">
                            <div>
                              <Label htmlFor="realm_id">Company ID (Realm ID)</Label>
                              <Input
                                id="realm_id"
                                type="text"
                                autoComplete="off"
                                value={qbConnectionData.realm_id}
                                onChange={(e) => setQbConnectionData({ ...qbConnectionData, realm_id: e.target.value })}
                                placeholder="Enter your QuickBooks Company ID"
                              />
                            </div>
                            <div>
                              <Label htmlFor="client_id">Client ID</Label>
                              <Input
                                id="client_id"
                                type="text"
                                autoComplete="off"
                                value={qbConnectionData.client_id}
                                onChange={(e) => setQbConnectionData({ ...qbConnectionData, client_id: e.target.value })}
                                placeholder="Enter your QuickBooks Client ID"
                              />
                            </div>
                            <div>
                              <Label htmlFor="client_secret">Client Secret</Label>
                              <Input
                                id="client_secret"
                                type="password"
                                autoComplete="new-password"
                                value={qbConnectionData.client_secret}
                                onChange={(e) => setQbConnectionData({ ...qbConnectionData, client_secret: e.target.value })}
                                placeholder="Enter your QuickBooks Client Secret"
                              />
                            </div>
                            <div>
                              <Label htmlFor="access_token">Access Token</Label>
                              <Input
                                id="access_token"
                                type="password"
                                autoComplete="new-password"
                                value={qbConnectionData.access_token}
                                onChange={(e) => setQbConnectionData({ ...qbConnectionData, access_token: e.target.value })}
                                placeholder="Enter access token"
                              />
                            </div>
                            <div>
                              <Label htmlFor="refresh_token">Refresh Token</Label>
                              <Input
                                id="refresh_token"
                                type="password"
                                autoComplete="new-password"
                                value={qbConnectionData.refresh_token}
                                onChange={(e) => setQbConnectionData({ ...qbConnectionData, refresh_token: e.target.value })}
                                placeholder="Enter refresh token"
                              />
                            </div>
                            <div>
                              <Label htmlFor="token_expires_at">Token Expires At</Label>
                              <Input
                                id="token_expires_at"
                                type="datetime-local"
                                autoComplete="off"
                                value={qbConnectionData.token_expires_at}
                                onChange={(e) => setQbConnectionData({ ...qbConnectionData, token_expires_at: e.target.value })}
                                placeholder="Token expiration date and time"
                              />
                            </div>
                            <div>
                              <Label htmlFor="environment">Environment</Label>
                              <Select
                                value={qbConnectionData.environment}
                                onValueChange={(value) => setQbConnectionData({ ...qbConnectionData, environment: value as "sandbox" | "production" })}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="sandbox">Sandbox (Development)</SelectItem>
                                  <SelectItem value="production">Production</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </form>

                          {qbConnectionError && (
                            <div className="bg-red-50 border border-red-200 rounded-md p-3 mt-4">
                              <p className="text-sm text-red-600">{qbConnectionError}</p>
                            </div>
                          )}

                          <div className="flex justify-end space-x-2 pt-4">
                            <Button variant="outline" onClick={() => setShowQBConnect(false)} disabled={isConnectingQB}>
                              Cancel
                            </Button>
                            <Button
                              type="button"
                              onClick={(e) => {
                                e.preventDefault()
                                e.stopPropagation()
                                handleQBConnect(e)
                              }}
                              className="bg-green-600 hover:bg-green-700"
                              disabled={isConnectingQB}
                            >
                              {isConnectingQB ? (
                                <>
                                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                                  Connecting...
                                </>
                              ) : (
                                <>
                                  <QuickBooksLogo width={16} height={16} className="mr-2" />
                                  Connect QuickBooks
                                </>
                              )}
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                {qbIntegration.connected ? (
                  <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">

                    <div className="flex items-start justify-between mb-6">
                      <div className="flex items-center space-x-4">
                        <div className="w-14 h-14 bg-gray-50 rounded-xl flex items-center justify-center border border-gray-200">
                          <QuickBooksLogo width={32} height={32} />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-1">QuickBooks Online</h3>
                          <p className="text-sm text-gray-600">{qbIntegration.companyName}</p>
                          <p className="text-xs text-gray-500 mt-0.5">{qbIntegration.environment} Environment</p>
                        </div>
                      </div>


                      <div className="flex flex-col items-end space-y-3">
                        <Badge variant="secondary" className="bg-green-100 text-green-800 px-3 py-1">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                          Connected
                        </Badge>
                        <div className="flex items-center space-x-2">
                          <Button variant="outline" size="sm" className="bg-white hover:bg-gray-50">
                            <RotateCcw className="h-4 w-4 mr-2" />
                            Sync Now
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="bg-white text-red-600 hover:bg-red-50"
                            onClick={handleQBDisconnect}
                          >
                            <X className="h-4 w-4 mr-2" />
                            Disconnect
                          </Button>
                        </div>
                      </div>
                    </div>


                    <div className="grid grid-cols-3 gap-4 mb-6">
                      <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600 mb-1">1,247</div>
                          <div className="text-xs text-gray-600">Vendors Synced</div>
                        </div>
                      </div>
                      <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600 mb-1">89</div>
                          <div className="text-xs text-gray-600">Accounts Synced</div>
                        </div>
                      </div>
                      <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600 mb-1">342</div>
                          <div className="text-xs text-gray-600">Items Synced</div>
                        </div>
                      </div>
                    </div>


                    <div className="bg-gray-50 rounded-lg p-5 border border-gray-200">
                      <h4 className="font-medium text-gray-900 mb-4 flex items-center">
                        <Database className="h-4 w-4 mr-2 text-green-600" />
                        Connection Details
                      </h4>
                      <div className="grid grid-cols-2 gap-6">
                        <div className="space-y-3">
                          <div>
                            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Company ID</span>
                            <p className="font-mono text-sm bg-gray-50 p-2 rounded mt-1 border">{qbIntegration.realmId}</p>
                          </div>
                          <div>
                            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Connected</span>
                            <p className="text-sm font-medium text-gray-900 mt-1">{qbIntegration.connectedAt}</p>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <div>
                            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Last Sync</span>
                            <p className="text-sm font-medium text-gray-900 mt-1">{qbIntegration.lastSync}</p>
                          </div>
                          <div>
                            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Sync Status</span>
                            <div className="flex items-center mt-1">
                              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                              <span className="text-sm font-medium text-green-700">All systems operational</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-xl">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <QuickBooksLogo width={36} height={36} />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No accounting system connected</h3>
                    <p className="text-gray-600 mb-4">Connect QuickBooks Online to automatically sync your invoice data.</p>
                    <Button onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      console.log('Connect QuickBooks button clicked')
                      setQbConnectionError("")
                      // Reset form data when opening
                      setQbConnectionData({
                        realm_id: "",
                        client_id: "",
                        client_secret: "",
                        access_token: "",
                        refresh_token: "",
                        token_expires_at: "",
                        environment: "sandbox",
                      })
                      setShowQBConnect(true)
                    }} className="bg-green-600 hover:bg-green-700">
                      <QuickBooksLogo width={16} height={16} className="mr-2" />
                      Connect QuickBooks
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* System Settings Tab */}
          <TabsContent value="system" className="space-y-6">
            <div className="grid grid-cols-2 gap-6">
              {/* Data Retention Settings */}
              <Card className="bg-white shadow-sm">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
                    <Database className="h-5 w-5 mr-2" />
                    Data Retention
                  </CardTitle>
                  <p className="text-sm text-gray-600 mt-1">Configure how long data is stored in the system</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="invoiceRetention">Invoice Data (days)</Label>
                    <Input
                      id="invoiceRetention"
                      type="number"
                      value={systemSettings.dataRetention.invoiceData}
                      onChange={(e) => handleDataRetentionChange('invoiceData', parseInt(e.target.value))}
                      className="mt-1"
                    />
                    <p className="text-xs text-gray-500 mt-1">How long to keep processed invoice data</p>
                  </div>
                  <div>
                    <Label htmlFor="auditRetention">Audit Logs (days)</Label>
                    <Input
                      id="auditRetention"
                      type="number"
                      value={systemSettings.dataRetention.auditLogs}
                      onChange={(e) => handleDataRetentionChange('auditLogs', parseInt(e.target.value))}
                      className="mt-1"
                    />
                    <p className="text-xs text-gray-500 mt-1">How long to keep audit trail records</p>
                  </div>
                  <div>
                    <Label htmlFor="activityRetention">User Activity (days)</Label>
                    <Input
                      id="activityRetention"
                      type="number"
                      value={systemSettings.dataRetention.userActivity}
                      onChange={(e) => handleDataRetentionChange('userActivity', parseInt(e.target.value))}
                      className="mt-1"
                    />
                    <p className="text-xs text-gray-500 mt-1">How long to keep user activity logs</p>
                  </div>
                </CardContent>
              </Card>

              {/* System Features */}
              <Card className="bg-white shadow-sm">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
                    <Settings className="h-5 w-5 mr-2" />
                    System Features
                  </CardTitle>
                  <p className="text-sm text-gray-600 mt-1">Enable or disable system-wide features</p>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="auditLogging" className="text-sm font-medium">Audit Logging</Label>
                      <p className="text-xs text-gray-500">Track all user actions and system changes</p>
                    </div>
                    <Switch
                      id="auditLogging"
                      checked={systemSettings.auditLogging}
                      onCheckedChange={(checked) => handleSystemSettingChange('auditLogging', checked)}
                    />
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="emailNotifications" className="text-sm font-medium">Email Notifications</Label>
                      <p className="text-xs text-gray-500">Send email alerts for important events</p>
                    </div>
                    <Switch
                      id="emailNotifications"
                      checked={systemSettings.emailNotifications}
                      onCheckedChange={(checked) => handleSystemSettingChange('emailNotifications', checked)}
                    />
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="autoProcessing" className="text-sm font-medium">Auto Processing</Label>
                      <p className="text-xs text-gray-500">Automatically process incoming invoices</p>
                    </div>
                    <Switch
                      id="autoProcessing"
                      checked={systemSettings.autoProcessing}
                      onCheckedChange={(checked) => handleSystemSettingChange('autoProcessing', checked)}
                    />
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="maintenanceMode" className="text-sm font-medium">Maintenance Mode</Label>
                      <p className="text-xs text-gray-500">Temporarily disable system for maintenance</p>
                    </div>
                    <Switch
                      id="maintenanceMode"
                      checked={systemSettings.maintenanceMode}
                      onCheckedChange={(checked) => handleSystemSettingChange('maintenanceMode', checked)}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Backup & Maintenance */}
            <Card className="bg-white shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
                  <Activity className="h-5 w-5 mr-2" />
                  Backup & Maintenance
                </CardTitle>
                <p className="text-sm text-gray-600 mt-1">Configure system backup and maintenance schedules</p>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-6">
                  <div>
                    <Label htmlFor="backupFrequency">Backup Frequency</Label>
                    <Select
                      value={systemSettings.backupFrequency}
                      onValueChange={(value) => handleSystemSettingChange('backupFrequency', value)}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="hourly">Hourly</SelectItem>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-end">
                    <Button variant="outline" className="w-full">
                      <RotateCcw className="h-4 w-4 mr-2" />
                      Backup Now
                    </Button>
                  </div>
                  <div className="flex items-end">
                    <Button variant="outline" className="w-full">
                      <FileText className="h-4 w-4 mr-2" />
                      View Logs
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
